-- Create a secure function to get users with auth data for admin dashboard
-- This function joins profiles with auth.users to get complete user information
-- Only accessible by admin users

CREATE OR REPLACE FUNCTION public.get_users_with_auth_data()
RETURNS TABLE (
    id UUID,
    email TEXT,
    full_name TEXT,
    company_name TEXT,
    phone TEXT,
    address TEXT,
    city TEXT,
    country TEXT,
    tax_identification_number TEXT,
    role TEXT,
    avatar_url TEXT,
    postal_code TEXT,
    business_email TEXT,
    business_phone TEXT,
    business_website TEXT,
    business_registration_number TEXT,
    business_logo_url TEXT,
    vat_registered BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    last_sign_in_at TIMESTAMPTZ,
    email_verified BOOLEAN,
    raw_user_meta_data JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    is_admin_user BOOLEAN := FALSE;
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Check if current user is admin
    -- First check user_roles table if it exists
    BEGIN
        SELECT EXISTS(
            SELECT 1 FROM user_roles 
            WHERE user_id = current_user_id AND role = 'admin'
        ) INTO is_admin_user;
    EXCEPTION WHEN undefined_table THEN
        -- If user_roles table doesn't exist, check environment variable
        is_admin_user := FALSE;
    END;
    
    -- Also check if user email is in admin emails environment variable
    IF NOT is_admin_user THEN
        SELECT EXISTS(
            SELECT 1 FROM auth.users au
            WHERE au.id = current_user_id
            AND LOWER(au.email) = ANY(
                string_to_array(
                    LOWER(COALESCE(current_setting('app.admin_emails', true), '')), 
                    ','
                )
            )
        ) INTO is_admin_user;
    END IF;
    
    -- Only allow admin users to access this function
    IF NOT is_admin_user THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    -- Return ALL profiles with auth data where available (LEFT JOIN)
    -- This ensures we show all 42 users from profiles table
    RETURN QUERY
    SELECT
        p.id,
        COALESCE(au.email, p.business_email, 'user-' || SUBSTRING(p.id::text, 1, 8) || '@profiles.local') as email,
        p.full_name,
        p.company_name,
        p.phone,
        p.address,
        p.city,
        p.country,
        p.tax_identification_number,
        p.role,
        p.avatar_url,
        p.postal_code,
        p.business_email,
        p.business_phone,
        p.business_website,
        p.business_registration_number,
        p.business_logo_url,
        p.vat_registered,
        p.created_at,
        p.updated_at,
        au.last_sign_in_at,
        COALESCE(au.email_confirmed_at IS NOT NULL, p.business_email IS NOT NULL, FALSE) as email_verified,
        COALESCE(au.raw_user_meta_data, '{}'::jsonb) as raw_user_meta_data
    FROM profiles p
    LEFT JOIN auth.users au ON p.id = au.id AND au.deleted_at IS NULL
    ORDER BY p.created_at DESC;
END;
$$;

-- Grant execute permission to authenticated users
-- The function itself handles admin verification
GRANT EXECUTE ON FUNCTION public.get_users_with_auth_data() TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION public.get_users_with_auth_data() IS 'Returns complete user data by joining profiles with auth.users. Only accessible by admin users.';
