-- Create a function to get all profiles for admin users
-- This bypasses <PERSON><PERSON> to ensure all 42 users are returned

CREATE OR REPLACE FUNCTION public.get_all_profiles_admin()
RETURNS SETOF profiles
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    is_admin_user BOOLEAN := FALSE;
    admin_emails TEXT[];
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Check if current user is admin
    -- First check user_roles table if it exists
    BEGIN
        SELECT EXISTS(
            SELECT 1 FROM user_roles 
            WHERE user_id = current_user_id AND role = 'admin'
        ) INTO is_admin_user;
    EXCEPTION WHEN undefined_table THEN
        -- If user_roles table doesn't exist, continue to email check
        is_admin_user := FALSE;
    END;
    
    -- Also check if user email is in admin emails
    IF NOT is_admin_user THEN
        -- Get admin emails from a secure source
        admin_emails := ARRAY['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        
        SELECT EXISTS(
            SELECT 1 FROM auth.users au
            WHERE au.id = current_user_id
            AND LOWER(au.email) = ANY(
                SELECT LOWER(unnest(admin_emails))
            )
        ) INTO is_admin_user;
    END IF;
    
    -- Only allow admin users to access this function
    IF NOT is_admin_user THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    -- Return ALL profiles without any RLS restrictions
    RETURN QUERY
    SELECT * FROM profiles
    ORDER BY created_at DESC;
END;
$$;

-- Grant execute permission to authenticated users
-- The function itself handles admin verification
GRANT EXECUTE ON FUNCTION public.get_all_profiles_admin() TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION public.get_all_profiles_admin() IS 'Returns all profiles bypassing RLS. Only accessible by admin users.';
